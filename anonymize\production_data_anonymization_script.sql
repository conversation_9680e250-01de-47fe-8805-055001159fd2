-- =====================================================================================================================
-- PRODUCTION DATA ANONYMIZATION SCRIPT
-- =====================================================================================================================
-- This script anonymizes sensitive personal data in the production database according to GDPR and data privacy requirements.
-- 
-- IMPORTANT WARNINGS:
-- 1. This script will PERMANENTLY modify data - ensure you have a complete backup before running
-- 2. Test this script thoroughly in a non-production environment first
-- 3. Review all anonymization logic to ensure it meets your specific compliance requirements
-- 4. Consider running during maintenance windows due to potential locking
--
-- Performance Optimizations:
-- - Uses bulk UPDATE operations instead of row-by-row processing
-- - Leverages efficient JOIN operations for data generation
-- - Implements transaction boundaries to allow rollback if needed
-- - Uses WITH (NOLOCK) hints where appropriate for read operations
-- =====================================================================================================================

SET NOCOUNT ON;
SET XACT_ABORT ON; -- Ensures automatic rollback on any error

DECLARE @StartTime DATETIME = GETDATE();
DECLARE @RowCount INT;
DECLARE @ErrorMessage NVARCHAR(4000);

PRINT '=== PRODUCTION DATA ANONYMIZATION STARTED AT ' + CONVERT(VARCHAR, @StartTime, 120) + ' ===';
PRINT '';

BEGIN TRY
    BEGIN TRANSACTION AnonymizationTransaction;
    
    -- =====================================================================================================================
    -- STEP 1: CREATE HELPER FUNCTIONS AND TEMPORARY DATA GENERATION
    -- =====================================================================================================================
    
    PRINT 'Step 1: Creating helper functions and seed data...';
    
    -- Create a numbers table for generating sequential fake data
    IF OBJECT_ID('tempdb..#Numbers') IS NOT NULL DROP TABLE #Numbers;
    WITH Numbers AS (
        SELECT 1 as n
        UNION ALL
        SELECT n + 1 FROM Numbers WHERE n < 10000
    )
    SELECT n INTO #Numbers FROM Numbers OPTION (MAXRECURSION 10000);
    
    -- Create temporary table for fake names
    IF OBJECT_ID('tempdb..#FakeNames') IS NOT NULL DROP TABLE #FakeNames;
    CREATE TABLE #FakeNames (
        ID INT IDENTITY(1,1),
        FirstName NVARCHAR(50),
        LastName NVARCHAR(50)
    );
    
    -- Insert diverse fake names
    INSERT INTO #FakeNames (FirstName, LastName) VALUES
    ('Alex', 'Johnson'), ('Blake', 'Smith'), ('Casey', 'Williams'), ('Drew', 'Brown'), ('Ellis', 'Jones'),
    ('Finley', 'Garcia'), ('Harley', 'Miller'), ('Indigo', 'Davis'), ('Jules', 'Rodriguez'), ('Kai', 'Martinez'),
    ('Lane', 'Hernandez'), ('Morgan', 'Lopez'), ('Nova', 'Gonzalez'), ('Ocean', 'Wilson'), ('Phoenix', 'Anderson'),
    ('Quinn', 'Thomas'), ('River', 'Taylor'), ('Sage', 'Moore'), ('Tatum', 'Jackson'), ('Unity', 'Martin'),
    ('Vale', 'Lee'), ('Winter', 'Perez'), ('Xen', 'Thompson'), ('York', 'White'), ('Zen', 'Harris'),
    ('Ash', 'Sanchez'), ('Bay', 'Clark'), ('Cloud', 'Ramirez'), ('Dale', 'Lewis'), ('Echo', 'Robinson'),
    ('Fox', 'Walker'), ('Grey', 'Young'), ('Haze', 'Allen'), ('Ivy', 'King'), ('Jazz', 'Wright'),
    ('Knox', 'Scott'), ('Lane', 'Torres'), ('Moon', 'Nguyen'), ('North', 'Hill'), ('Onyx', 'Flores'),
    ('Pine', 'Green'), ('Quest', 'Adams'), ('Rain', 'Nelson'), ('Stone', 'Baker'), ('Truth', 'Hall'),
    ('Urban', 'Rivera'), ('Violet', 'Campbell'), ('Wave', 'Mitchell'), ('Xylem', 'Carter'), ('Yarrow', 'Roberts'),
    ('Zion', 'Gomez'), ('Azure', 'Phillips'), ('Brook', 'Evans'), ('Coral', 'Turner'), ('Dawn', 'Diaz'),
    ('Eden', 'Parker'), ('Frost', 'Cruz'), ('Glen', 'Edwards'), ('Haven', 'Collins'), ('Ion', 'Reyes');
    
    -- Create temporary table for fake company names
    IF OBJECT_ID('tempdb..#FakeCompanies') IS NOT NULL DROP TABLE #FakeCompanies;
    CREATE TABLE #FakeCompanies (
        ID INT IDENTITY(1,1),
        CompanyName NVARCHAR(50)
    );
    
    INSERT INTO #FakeCompanies (CompanyName) VALUES
    ('Alpha Industries'), ('Beta Corp'), ('Gamma LLC'), ('Delta Systems'), ('Epsilon Ltd'),
    ('Zeta Technologies'), ('Eta Solutions'), ('Theta Enterprises'), ('Iota Group'), ('Kappa Inc'),
    ('Lambda Holdings'), ('Mu Partners'), ('Nu Ventures'), ('Xi Corporation'), ('Omicron Co'),
    ('Pi Dynamics'), ('Rho Industries'), ('Sigma Works'), ('Tau Systems'), ('Upsilon Ltd'),
    ('Phi Technologies'), ('Chi Solutions'), ('Psi Group'), ('Omega Corp'), ('Apex Industries'),
    ('Vertex Systems'), ('Matrix Corp'), ('Nexus Ltd'), ('Vortex Inc'), ('Zenith Holdings');
    
    PRINT 'Helper functions and seed data created successfully.';
    PRINT '';
    
    -- =====================================================================================================================
    -- STEP 2: ANONYMIZE PRIMARY PERSONAL INFORMATION TABLES
    -- =====================================================================================================================
    
    PRINT 'Step 2: Anonymizing primary personal information tables...';
    
    -- Anonymize Person table
    PRINT '  - Anonymizing Person table...';
    UPDATE p SET
        FirstName = fn.FirstName,
        LastName = fn.LastName,
        Email = CASE 
            WHEN p.Email IS NOT NULL THEN 
                'user' + CAST(ABS(CHECKSUM(NEWID())) % 100000 AS VARCHAR(10)) + '@anonymized.local'
            ELSE NULL 
        END,
        Phone = CASE 
            WHEN p.Phone IS NOT NULL THEN 
                '******-' + RIGHT('000' + CAST(ABS(CHECKSUM(NEWID())) % 10000 AS VARCHAR(4)), 4)
            ELSE NULL 
        END,
        Notes = CASE 
            WHEN p.Notes IS NOT NULL THEN 'Anonymized note entry'
            ELSE NULL 
        END,
        CustomerName = CASE 
            WHEN p.CustomerName IS NOT NULL THEN 'Anonymized Customer'
            ELSE NULL 
        END,
        LastMedicalDate = CASE 
            WHEN p.LastMedicalDate IS NOT NULL THEN 
                DATEADD(day, ABS(CHECKSUM(NEWID())) % 365, '2020-01-01')
            ELSE NULL 
        END,
        Photo = NULL,
        PhotoFileSize = NULL,
        PhotoInternalName = NULL
    FROM Person p
    INNER JOIN (
        SELECT Id, 
               ROW_NUMBER() OVER (ORDER BY Id) as RowNum
        FROM Person
    ) pr ON p.Id = pr.Id
    INNER JOIN (
        SELECT FirstName, LastName,
               ROW_NUMBER() OVER (ORDER BY ID) as RowNum
        FROM #FakeNames
    ) fn ON (pr.RowNum - 1) % (SELECT COUNT(*) FROM #FakeNames) + 1 = fn.RowNum;
    
    SET @RowCount = @@ROWCOUNT;
    PRINT '    Updated ' + CAST(@RowCount AS VARCHAR(10)) + ' records in Person table.';
    
    -- Anonymize ContactPersonInformation table
    PRINT '  - Anonymizing ContactPersonInformation table...';
    UPDATE cpi SET
        FirstName = fn.FirstName,
        LastName = fn.LastName,
        Email = 'contact' + CAST(ABS(CHECKSUM(NEWID())) % 100000 AS VARCHAR(10)) + '@anonymized.local',
        PhoneNo = '******-' + RIGHT('000' + CAST(ABS(CHECKSUM(NEWID())) % 10000 AS VARCHAR(4)), 4),
        Address = 
            CAST(ABS(CHECKSUM(NEWID())) % 9999 + 1 AS VARCHAR(4)) + ' ' +
            CASE ABS(CHECKSUM(NEWID())) % 10
                WHEN 0 THEN 'Main St'
                WHEN 1 THEN 'Oak Ave'
                WHEN 2 THEN 'Park Rd'
                WHEN 3 THEN 'First St'
                WHEN 4 THEN 'Second Ave'
                WHEN 5 THEN 'Third Blvd'
                WHEN 6 THEN 'Fourth Way'
                WHEN 7 THEN 'Fifth Pl'
                WHEN 8 THEN 'Sixth Dr'
                ELSE 'Seventh Ln'
            END + ', Anytown, ST 12345'
    FROM ContactPersonInformation cpi
    INNER JOIN (
        SELECT Id, 
               ROW_NUMBER() OVER (ORDER BY Id) as RowNum
        FROM ContactPersonInformation
    ) cpr ON cpi.Id = cpr.Id
    INNER JOIN (
        SELECT FirstName, LastName,
               ROW_NUMBER() OVER (ORDER BY ID) as RowNum
        FROM #FakeNames
    ) fn ON (cpr.RowNum - 1) % (SELECT COUNT(*) FROM #FakeNames) + 1 = fn.RowNum;
    
    SET @RowCount = @@ROWCOUNT;
    PRINT '    Updated ' + CAST(@RowCount AS VARCHAR(10)) + ' records in ContactPersonInformation table.';
    
    -- Anonymize GOSecurity.GOUser table
    PRINT '  - Anonymizing GOSecurity.GOUser table...';
    UPDATE gu SET
        UserName = 'user' + CAST(ABS(CHECKSUM(NEWID())) % 100000 AS VARCHAR(10)),
        EmailAddress = 'gouser' + CAST(ABS(CHECKSUM(NEWID())) % 100000 AS VARCHAR(10)) + '@anonymized.local',
        NewEmailAddress = CASE 
            WHEN gu.NewEmailAddress IS NOT NULL THEN 
                'newuser' + CAST(ABS(CHECKSUM(NEWID())) % 100000 AS VARCHAR(10)) + '@anonymized.local'
            ELSE NULL 
        END,
        FullName = fn.FirstName + ' ' + fn.LastName,
        FirstName = fn.FirstName,
        LastName = fn.LastName,
        Password = 'ANONYMIZED_PASSWORD_HASH_' + CAST(ABS(CHECKSUM(NEWID())) % 1000000 AS VARCHAR(10)),
        ExternalUserId = CASE 
            WHEN gu.ExternalUserId IS NOT NULL THEN 
                'ext_' + CAST(ABS(CHECKSUM(NEWID())) % 1000000 AS VARCHAR(10))
            ELSE NULL 
        END
    FROM [GOSecurity].[GOUser] gu
    INNER JOIN (
        SELECT Id, 
               ROW_NUMBER() OVER (ORDER BY Id) as RowNum
        FROM [GOSecurity].[GOUser]
    ) gur ON gu.Id = gur.Id
    INNER JOIN (
        SELECT FirstName, LastName,
               ROW_NUMBER() OVER (ORDER BY ID) as RowNum
        FROM #FakeNames
    ) fn ON (gur.RowNum - 1) % (SELECT COUNT(*) FROM #FakeNames) + 1 = fn.RowNum;
    
    SET @RowCount = @@ROWCOUNT;
    PRINT '    Updated ' + CAST(@RowCount AS VARCHAR(10)) + ' records in GOSecurity.GOUser table.';
    
    -- Anonymize WebsiteUser table
    PRINT '  - Anonymizing WebsiteUser table...';
    UPDATE WebsiteUser SET
        Username = 'webuser' + CAST(ABS(CHECKSUM(NEWID())) % 100000 AS VARCHAR(10)),
        Password = 'ANONYMIZED_WEB_PASSWORD_' + CAST(ABS(CHECKSUM(NEWID())) % 1000000 AS VARCHAR(10));
    
    SET @RowCount = @@ROWCOUNT;
    PRINT '    Updated ' + CAST(@RowCount AS VARCHAR(10)) + ' records in WebsiteUser table.';
    
    PRINT 'Step 2 completed successfully.';
    PRINT '';
    
    -- =====================================================================================================================
    -- STEP 3: ANONYMIZE SECONDARY PERSONAL INFORMATION
    -- =====================================================================================================================
    
    PRINT 'Step 3: Anonymizing secondary personal information...';
    
    -- Anonymize Customer table
    PRINT '  - Anonymizing Customer table...';
    UPDATE c SET
        ContactNumber = CASE 
            WHEN c.ContactNumber IS NOT NULL THEN 
                '******-' + RIGHT('000' + CAST(ABS(CHECKSUM(NEWID())) % 10000 AS VARCHAR(4)), 4)
            ELSE NULL 
        END,
        Email = CASE 
            WHEN c.Email IS NOT NULL THEN 
                'customer' + CAST(ABS(CHECKSUM(NEWID())) % 100000 AS VARCHAR(10)) + '@anonymized.local'
            ELSE NULL 
        END,
        Addess = CASE 
            WHEN c.Addess IS NOT NULL THEN 
                CAST(ABS(CHECKSUM(NEWID())) % 9999 + 1 AS VARCHAR(4)) + ' Business Park Dr, Corporate City, ST 12345'
            ELSE NULL 
        END,
        CompanyName = fc.CompanyName
    FROM Customer c
    INNER JOIN (
        SELECT Id, 
               ROW_NUMBER() OVER (ORDER BY Id) as RowNum
        FROM Customer
    ) cr ON c.Id = cr.Id
    INNER JOIN (
        SELECT CompanyName,
               ROW_NUMBER() OVER (ORDER BY ID) as RowNum
        FROM #FakeCompanies
    ) fc ON (cr.RowNum - 1) % (SELECT COUNT(*) FROM #FakeCompanies) + 1 = fc.RowNum;
    
    SET @RowCount = @@ROWCOUNT;
    PRINT '    Updated ' + CAST(@RowCount AS VARCHAR(10)) + ' records in Customer table.';
    
    -- Anonymize Card table
    PRINT '  - Anonymizing Card table...';
    UPDATE Card SET
        CardNumber = 'CARD' + RIGHT('0000000' + CAST(ABS(CHECKSUM(NEWID())) % 10000000 AS VARCHAR(7)), 7),
        Weigand = CASE 
            WHEN Weigand IS NOT NULL THEN 
                CAST(ABS(CHECKSUM(NEWID())) % 100000 AS VARCHAR(10))
            ELSE NULL 
        END,
        FacilityCode = CASE 
            WHEN FacilityCode IS NOT NULL THEN 
                RIGHT('00' + CAST(ABS(CHECKSUM(NEWID())) % 1000 AS VARCHAR(3)), 3)
            ELSE NULL 
        END;
    
    SET @RowCount = @@ROWCOUNT;
    PRINT '    Updated ' + CAST(@RowCount AS VARCHAR(10)) + ' records in Card table.';
    
    -- Anonymize LicenceDetail table
    PRINT '  - Anonymizing LicenceDetail table...';
    UPDATE LicenceDetail SET
        LicenseNumber = CASE 
            WHEN LicenseNumber IS NOT NULL THEN 
                'DL' + RIGHT('00000000' + CAST(ABS(CHECKSUM(NEWID())) % 100000000 AS VARCHAR(8)), 8)
            ELSE NULL 
        END,
        Document = NULL,
        DocumentFileSize = NULL,
        DocumentInternalName = NULL;
    
    SET @RowCount = @@ROWCOUNT;
    PRINT '    Updated ' + CAST(@RowCount AS VARCHAR(10)) + ' records in LicenceDetail table.';
    
    -- Anonymize Module table
    PRINT '  - Anonymizing Module table...';
    UPDATE Module SET
        SimCardNumber = CASE 
            WHEN SimCardNumber IS NOT NULL THEN 
                'SIM' + RIGHT('000000000000000' + CAST(ABS(CHECKSUM(NEWID())) % CAST('1000000000000000' AS BIGINT) AS VARCHAR(15)), 15)
            ELSE NULL 
        END,
        CCID = CASE 
            WHEN CCID IS NOT NULL THEN 
                'CCID' + RIGHT('00000000000000000000' + CAST(ABS(CHECKSUM(NEWID())) % CAST('100000000000000000000' AS BIGINT) AS VARCHAR(20)), 20)
            ELSE NULL 
        END,
        TechNumber = CASE 
            WHEN TechNumber IS NOT NULL THEN 
                'TECH' + RIGHT('000000' + CAST(ABS(CHECKSUM(NEWID())) % 1000000 AS VARCHAR(6)), 6)
            ELSE NULL 
        END,
        Note = CASE 
            WHEN Note IS NOT NULL THEN 'Anonymized technical note'
            ELSE NULL 
        END;
    
    SET @RowCount = @@ROWCOUNT;
    PRINT '    Updated ' + CAST(@RowCount AS VARCHAR(10)) + ' records in Module table.';
    
    -- Anonymize GOSecurity.GOUser2FA table
    PRINT '  - Anonymizing GOSecurity.GOUser2FA table...';
    UPDATE [GOSecurity].[GOUser2FA] SET
        OTPSecret = CASE 
            WHEN OTPSecret IS NOT NULL THEN 
                'ANON_OTP_' + CAST(ABS(CHECKSUM(NEWID())) % 1000000 AS VARCHAR(10))
            ELSE NULL 
        END;
    
    SET @RowCount = @@ROWCOUNT;
    PRINT '    Updated ' + CAST(@RowCount AS VARCHAR(10)) + ' records in GOSecurity.GOUser2FA table.';
    
    -- Anonymize GOSecurity.GOLoginHistory table
    PRINT '  - Anonymizing GOSecurity.GOLoginHistory table...';
    UPDATE [GOSecurity].[GOLoginHistory] SET
        [User] = 'anonuser' + CAST(ABS(CHECKSUM(NEWID())) % 100000 AS VARCHAR(10)),
        Info = 'Anonymized login info';
    
    SET @RowCount = @@ROWCOUNT;
    PRINT '    Updated ' + CAST(@RowCount AS VARCHAR(10)) + ' records in GOSecurity.GOLoginHistory table.';
    
    PRINT 'Step 3 completed successfully.';
    PRINT '';
    
    -- =====================================================================================================================
    -- STEP 4: ANONYMIZE LOCATION AND ACTIVITY DATA
    -- =====================================================================================================================
    
    PRINT 'Step 4: Anonymizing location and activity data...';
    
    -- Anonymize GPSHistory table - offset coordinates by random amounts
    PRINT '  - Anonymizing GPSHistory table...';
    UPDATE GPSHistory SET
        Latitude = CASE 
            WHEN Latitude IS NOT NULL THEN 
                40 + (ABS(CHECKSUM(NEWID())) % 100) / 100.0  -- Random latitude around 40° (generic US location)
            ELSE NULL 
        END,
        Longitude = CASE 
            WHEN Longitude IS NOT NULL THEN 
                -100 + (ABS(CHECKSUM(NEWID())) % 100) / 100.0  -- Random longitude around -100° (generic US location)
            ELSE NULL 
        END;
    
    SET @RowCount = @@ROWCOUNT;
    PRINT '    Updated ' + CAST(@RowCount AS VARCHAR(10)) + ' records in GPSHistory table.';
    
    -- Anonymize VehicleGPS table - offset coordinates by random amounts
    PRINT '  - Anonymizing VehicleGPS table...';
    UPDATE VehicleGPS SET
        Latitude = 40 + (ABS(CHECKSUM(NEWID())) % 100) / 100.0,  -- Random latitude around 40°
        Longitude = -100 + (ABS(CHECKSUM(NEWID())) % 100) / 100.0;  -- Random longitude around -100°
    
    SET @RowCount = @@ROWCOUNT;
    PRINT '    Updated ' + CAST(@RowCount AS VARCHAR(10)) + ' records in VehicleGPS table.';
    
    -- Note: Session and Impact tables contain behavioral data but not direct personal identifiers
    -- The foreign key relationships will maintain referential integrity while the linked personal data is anonymized
    
    PRINT 'Step 4 completed successfully.';
    PRINT '';
    
    -- =====================================================================================================================
    -- STEP 5: ANONYMIZE AUDIT AND TRACKING INFORMATION
    -- =====================================================================================================================
    
    PRINT 'Step 5: Anonymizing audit and tracking information...';
    
    -- Anonymize GOChangeTracking.CustomerAudit table
    -- Note: We preserve the GUID structure but replace with anonymized GUIDs to maintain referential integrity
    PRINT '  - Anonymizing GOChangeTracking.CustomerAudit table...';
    IF EXISTS (SELECT 1 FROM sys.tables t JOIN sys.schemas s ON t.schema_id = s.schema_id 
               WHERE s.name = 'GOChangeTracking' AND t.name = 'CustomerAudit')
    BEGIN
        -- Create mapping table for consistent GUID replacement
        IF OBJECT_ID('tempdb..#UserGuidMapping') IS NOT NULL DROP TABLE #UserGuidMapping;
        CREATE TABLE #UserGuidMapping (
            OriginalGuid UNIQUEIDENTIFIER,
            AnonymizedGuid UNIQUEIDENTIFIER
        );
        
        -- Generate mappings for all unique user GUIDs found in audit tables
        INSERT INTO #UserGuidMapping (OriginalGuid, AnonymizedGuid)
        SELECT DISTINCT u.OriginalGuid, NEWID() as AnonymizedGuid
        FROM (
            SELECT CreatedBy as OriginalGuid FROM [GOChangeTracking].[CustomerAudit] WHERE CreatedBy IS NOT NULL
            UNION
            SELECT LastModifiedBy FROM [GOChangeTracking].[CustomerAudit] WHERE LastModifiedBy IS NOT NULL
            UNION
            SELECT DeletedBy FROM [GOChangeTracking].[CustomerAudit] WHERE DeletedBy IS NOT NULL
        ) u;
        
        -- Update CustomerAudit with anonymized GUIDs
        UPDATE ca SET
            CreatedBy = COALESCE(ugm1.AnonymizedGuid, ca.CreatedBy),
            LastModifiedBy = COALESCE(ugm2.AnonymizedGuid, ca.LastModifiedBy),
            DeletedBy = COALESCE(ugm3.AnonymizedGuid, ca.DeletedBy)
        FROM [GOChangeTracking].[CustomerAudit] ca
        LEFT JOIN #UserGuidMapping ugm1 ON ca.CreatedBy = ugm1.OriginalGuid
        LEFT JOIN #UserGuidMapping ugm2 ON ca.LastModifiedBy = ugm2.OriginalGuid
        LEFT JOIN #UserGuidMapping ugm3 ON ca.DeletedBy = ugm3.OriginalGuid;
        
        SET @RowCount = @@ROWCOUNT;
        PRINT '    Updated ' + CAST(@RowCount AS VARCHAR(10)) + ' records in GOChangeTracking.CustomerAudit table.';
    END
    ELSE
    BEGIN
        PRINT '    GOChangeTracking.CustomerAudit table not found - skipping.';
    END
    
    -- Anonymize GOChangeTracking.GOChangeDelta table
    PRINT '  - Anonymizing GOChangeTracking.GOChangeDelta table...';
    IF EXISTS (SELECT 1 FROM sys.tables t JOIN sys.schemas s ON t.schema_id = s.schema_id 
               WHERE s.name = 'GOChangeTracking' AND t.name = 'GOChangeDelta')
    BEGIN
        UPDATE [GOChangeTracking].[GOChangeDelta] SET
            Who = 'anonymized_user_' + CAST(ABS(CHECKSUM(NEWID())) % 100000 AS VARCHAR(10));
        
        SET @RowCount = @@ROWCOUNT;
        PRINT '    Updated ' + CAST(@RowCount AS VARCHAR(10)) + ' records in GOChangeTracking.GOChangeDelta table.';
    END
    ELSE
    BEGIN
        PRINT '    GOChangeTracking.GOChangeDelta table not found - skipping.';
    END
    
    PRINT 'Step 5 completed successfully.';
    PRINT '';
    
    -- =====================================================================================================================
    -- STEP 6: CLEAN UP TEMPORARY OBJECTS
    -- =====================================================================================================================
    
    PRINT 'Step 6: Cleaning up temporary objects...';
    
    DROP TABLE #Numbers;
    DROP TABLE #FakeNames;
    DROP TABLE #FakeCompanies;
    IF OBJECT_ID('tempdb..#UserGuidMapping') IS NOT NULL DROP TABLE #UserGuidMapping;
    
    PRINT 'Temporary objects cleaned up successfully.';
    PRINT '';
    
    -- =====================================================================================================================
    -- COMMIT TRANSACTION AND COMPLETION
    -- =====================================================================================================================
    
    COMMIT TRANSACTION AnonymizationTransaction;
    
    DECLARE @EndTime DATETIME = GETDATE();
    DECLARE @Duration VARCHAR(20) = CAST(DATEDIFF(SECOND, @StartTime, @EndTime) AS VARCHAR(10));
    
    PRINT '=== ANONYMIZATION COMPLETED SUCCESSFULLY ===';
    PRINT 'Start Time: ' + CONVERT(VARCHAR, @StartTime, 120);
    PRINT 'End Time: ' + CONVERT(VARCHAR, @EndTime, 120);
    PRINT 'Duration: ' + @Duration + ' seconds';
    PRINT '';
    PRINT 'All personal data has been anonymized according to the specified requirements.';
    PRINT 'Referential integrity has been maintained throughout the process.';
    
END TRY
BEGIN CATCH
    -- =====================================================================================================================
    -- ERROR HANDLING AND ROLLBACK
    -- =====================================================================================================================
    
    IF @@TRANCOUNT > 0
    BEGIN
        ROLLBACK TRANSACTION AnonymizationTransaction;
        PRINT 'TRANSACTION ROLLED BACK due to error.';
    END
    
    SET @ErrorMessage = ERROR_MESSAGE();
    DECLARE @ErrorNumber INT = ERROR_NUMBER();
    DECLARE @ErrorSeverity INT = ERROR_SEVERITY();
    DECLARE @ErrorState INT = ERROR_STATE();
    DECLARE @ErrorLine INT = ERROR_LINE();
    
    PRINT '';
    PRINT '=== ANONYMIZATION FAILED ===';
    PRINT 'Error Number: ' + CAST(@ErrorNumber AS VARCHAR(10));
    PRINT 'Error Severity: ' + CAST(@ErrorSeverity AS VARCHAR(10));
    PRINT 'Error State: ' + CAST(@ErrorState AS VARCHAR(10));
    PRINT 'Error Line: ' + CAST(@ErrorLine AS VARCHAR(10));
    PRINT 'Error Message: ' + @ErrorMessage;
    PRINT '';
    PRINT 'All changes have been rolled back. No data was modified.';
    PRINT 'Please review the error and retry the anonymization process.';
    
    -- Clean up temporary objects even in error case
    IF OBJECT_ID('tempdb..#Numbers') IS NOT NULL DROP TABLE #Numbers;
    IF OBJECT_ID('tempdb..#FakeNames') IS NOT NULL DROP TABLE #FakeNames;
    IF OBJECT_ID('tempdb..#FakeCompanies') IS NOT NULL DROP TABLE #FakeCompanies;
    IF OBJECT_ID('tempdb..#UserGuidMapping') IS NOT NULL DROP TABLE #UserGuidMapping;
    
    RAISERROR(@ErrorMessage, @ErrorSeverity, @ErrorState);
END CATCH

SET NOCOUNT OFF;

-- =====================================================================================================================
-- POST-ANONYMIZATION VERIFICATION QUERIES
-- =====================================================================================================================
-- Uncomment and run these queries after anonymization to verify the results:

/*
-- Verify Person table anonymization
SELECT TOP 5 FirstName, LastName, Email, Phone FROM Person;

-- Verify Customer table anonymization  
SELECT TOP 5 CompanyName, Email, ContactNumber FROM Customer;

-- Verify GOUser table anonymization
SELECT TOP 5 UserName, EmailAddress, FullName FROM [GOSecurity].[GOUser];

-- Check for any remaining potentially sensitive data patterns
SELECT 'Person' as TableName, COUNT(*) as Records,
       SUM(CASE WHEN Email LIKE '%@anonymized.local' THEN 1 ELSE 0 END) as AnonymizedEmails
FROM Person
WHERE Email IS NOT NULL
UNION ALL
SELECT 'Customer', COUNT(*), 
       SUM(CASE WHEN Email LIKE '%@anonymized.local' THEN 1 ELSE 0 END)
FROM Customer  
WHERE Email IS NOT NULL
UNION ALL
SELECT 'GOUser', COUNT(*),
       SUM(CASE WHEN EmailAddress LIKE '%@anonymized.local' THEN 1 ELSE 0 END)
FROM [GOSecurity].[GOUser]
WHERE EmailAddress IS NOT NULL;
*/
