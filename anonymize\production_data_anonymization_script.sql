-- =====================================================================================================================
-- PRODUCTION DATA ANONYMIZATION SCRIPT
-- =====================================================================================================================
-- This script anonymizes sensitive personal data in the production database according to GDPR and data privacy requirements.
-- 
-- IMPORTANT WARNINGS:
-- 1. This script will PERMANENTLY modify data - ensure you have a complete backup before running
-- 2. Test this script thoroughly in a non-production environment first
-- 3. Review all anonymization logic to ensure it meets your specific compliance requirements
-- 4. Consider running during maintenance windows due to potential locking
--
-- Performance Optimizations:
-- - Uses bulk UPDATE operations instead of row-by-row processing
-- - Leverages efficient JOIN operations for data generation
-- - Implements transaction boundaries to allow rollback if needed
-- - Uses WITH (NOLOCK) hints where appropriate for read operations
-- =====================================================================================================================

SET NOCOUNT ON;
SET XACT_ABORT ON; -- Ensures automatic rollback on any error

DECLARE @StartTime DATETIME = GETDATE();
DECLARE @RowCount INT;
DECLARE @ErrorMessage NVARCHAR(4000);

PRINT '=== PRODUCTION DATA ANONYMIZATION STARTED AT ' + CONVERT(VARCHAR, @StartTime, 120) + ' ===';
PRINT '';

BEGIN TRY
    BEGIN TRANSACTION AnonymizationTransaction;
    
    -- =====================================================================================================================
    -- STEP 1: INITIALIZE ANONYMIZATION FUNCTIONS
    -- =====================================================================================================================
    
    PRINT 'Step 1: Initializing anonymization functions...';
    
    -- Helper function definitions (embedded in UPDATE statements for better performance)
    -- Names will be generated using modular arithmetic on CHECKSUM for consistency
    -- Company names will be generated using predefined patterns
    -- All data generation will be done directly in UPDATE statements
    
    PRINT 'Anonymization functions initialized successfully.';
    PRINT '';
    
    -- =====================================================================================================================
    -- STEP 2: ANONYMIZE PRIMARY PERSONAL INFORMATION TABLES
    -- =====================================================================================================================
    
    PRINT 'Step 2: Anonymizing primary personal information tables...';
    
    -- Anonymize Person table
    PRINT '  - Anonymizing Person table...';
    UPDATE Person SET
        FirstName = CASE ABS(CHECKSUM(NEWID())) % 50
            WHEN 0 THEN 'Alex' WHEN 1 THEN 'Blake' WHEN 2 THEN 'Casey' WHEN 3 THEN 'Drew' WHEN 4 THEN 'Ellis'
            WHEN 5 THEN 'Finley' WHEN 6 THEN 'Harley' WHEN 7 THEN 'Indigo' WHEN 8 THEN 'Jules' WHEN 9 THEN 'Kai'
            WHEN 10 THEN 'Lane' WHEN 11 THEN 'Morgan' WHEN 12 THEN 'Nova' WHEN 13 THEN 'Ocean' WHEN 14 THEN 'Phoenix'
            WHEN 15 THEN 'Quinn' WHEN 16 THEN 'River' WHEN 17 THEN 'Sage' WHEN 18 THEN 'Tatum' WHEN 19 THEN 'Unity'
            WHEN 20 THEN 'Vale' WHEN 21 THEN 'Winter' WHEN 22 THEN 'Xen' WHEN 23 THEN 'York' WHEN 24 THEN 'Zen'
            WHEN 25 THEN 'Ash' WHEN 26 THEN 'Bay' WHEN 27 THEN 'Cloud' WHEN 28 THEN 'Dale' WHEN 29 THEN 'Echo'
            WHEN 30 THEN 'Fox' WHEN 31 THEN 'Grey' WHEN 32 THEN 'Haze' WHEN 33 THEN 'Ivy' WHEN 34 THEN 'Jazz'
            WHEN 35 THEN 'Knox' WHEN 36 THEN 'Moon' WHEN 37 THEN 'North' WHEN 38 THEN 'Onyx' WHEN 39 THEN 'Pine'
            WHEN 40 THEN 'Quest' WHEN 41 THEN 'Rain' WHEN 42 THEN 'Stone' WHEN 43 THEN 'Truth' WHEN 44 THEN 'Urban'
            WHEN 45 THEN 'Violet' WHEN 46 THEN 'Wave' WHEN 47 THEN 'Xylem' WHEN 48 THEN 'Yarrow' ELSE 'Zion'
        END,
        LastName = CASE ABS(CHECKSUM(NEWID())) % 30
            WHEN 0 THEN 'Johnson' WHEN 1 THEN 'Smith' WHEN 2 THEN 'Williams' WHEN 3 THEN 'Brown' WHEN 4 THEN 'Jones'
            WHEN 5 THEN 'Garcia' WHEN 6 THEN 'Miller' WHEN 7 THEN 'Davis' WHEN 8 THEN 'Rodriguez' WHEN 9 THEN 'Martinez'
            WHEN 10 THEN 'Hernandez' WHEN 11 THEN 'Lopez' WHEN 12 THEN 'Gonzalez' WHEN 13 THEN 'Wilson' WHEN 14 THEN 'Anderson'
            WHEN 15 THEN 'Thomas' WHEN 16 THEN 'Taylor' WHEN 17 THEN 'Moore' WHEN 18 THEN 'Jackson' WHEN 19 THEN 'Martin'
            WHEN 20 THEN 'Lee' WHEN 21 THEN 'Perez' WHEN 22 THEN 'Thompson' WHEN 23 THEN 'White' WHEN 24 THEN 'Harris'
            WHEN 25 THEN 'Clark' WHEN 26 THEN 'Lewis' WHEN 27 THEN 'Robinson' WHEN 28 THEN 'Walker' WHEN 29 THEN 'Young'
        END,
        Email = CASE 
            WHEN Email IS NOT NULL THEN 
                'user' + CAST(ABS(CHECKSUM(NEWID())) % 100000 AS VARCHAR(10)) + '@anonymized.local'
            ELSE NULL 
        END,
        Phone = CASE 
            WHEN Phone IS NOT NULL THEN 
                '******-' + RIGHT('000' + CAST(ABS(CHECKSUM(NEWID())) % 10000 AS VARCHAR(4)), 4)
            ELSE NULL 
        END,
        Notes = CASE 
            WHEN Notes IS NOT NULL THEN 'Anonymized note entry'
            ELSE NULL 
        END,
        CustomerName = CASE 
            WHEN CustomerName IS NOT NULL THEN 'Anonymized Customer'
            ELSE NULL 
        END,
        LastMedicalDate = CASE 
            WHEN LastMedicalDate IS NOT NULL THEN 
                DATEADD(day, ABS(CHECKSUM(NEWID())) % 365, '2020-01-01')
            ELSE NULL 
        END,
        Photo = NULL,
        PhotoFileSize = NULL,
        PhotoInternalName = NULL;
    
    SET @RowCount = @@ROWCOUNT;
    PRINT '    Updated ' + CAST(@RowCount AS VARCHAR(10)) + ' records in Person table.';
    
    -- Anonymize ContactPersonInformation table
    PRINT '  - Anonymizing ContactPersonInformation table...';
    UPDATE ContactPersonInformation SET
        FirstName = CASE ABS(CHECKSUM(NEWID())) % 50
            WHEN 0 THEN 'Alex' WHEN 1 THEN 'Blake' WHEN 2 THEN 'Casey' WHEN 3 THEN 'Drew' WHEN 4 THEN 'Ellis'
            WHEN 5 THEN 'Finley' WHEN 6 THEN 'Harley' WHEN 7 THEN 'Indigo' WHEN 8 THEN 'Jules' WHEN 9 THEN 'Kai'
            WHEN 10 THEN 'Lane' WHEN 11 THEN 'Morgan' WHEN 12 THEN 'Nova' WHEN 13 THEN 'Ocean' WHEN 14 THEN 'Phoenix'
            WHEN 15 THEN 'Quinn' WHEN 16 THEN 'River' WHEN 17 THEN 'Sage' WHEN 18 THEN 'Tatum' WHEN 19 THEN 'Unity'
            WHEN 20 THEN 'Vale' WHEN 21 THEN 'Winter' WHEN 22 THEN 'Xen' WHEN 23 THEN 'York' WHEN 24 THEN 'Zen'
            WHEN 25 THEN 'Ash' WHEN 26 THEN 'Bay' WHEN 27 THEN 'Cloud' WHEN 28 THEN 'Dale' WHEN 29 THEN 'Echo'
            WHEN 30 THEN 'Fox' WHEN 31 THEN 'Grey' WHEN 32 THEN 'Haze' WHEN 33 THEN 'Ivy' WHEN 34 THEN 'Jazz'
            WHEN 35 THEN 'Knox' WHEN 36 THEN 'Moon' WHEN 37 THEN 'North' WHEN 38 THEN 'Onyx' WHEN 39 THEN 'Pine'
            WHEN 40 THEN 'Quest' WHEN 41 THEN 'Rain' WHEN 42 THEN 'Stone' WHEN 43 THEN 'Truth' WHEN 44 THEN 'Urban'
            WHEN 45 THEN 'Violet' WHEN 46 THEN 'Wave' WHEN 47 THEN 'Xylem' WHEN 48 THEN 'Yarrow' ELSE 'Zion'
        END,
        LastName = CASE ABS(CHECKSUM(NEWID())) % 30
            WHEN 0 THEN 'Johnson' WHEN 1 THEN 'Smith' WHEN 2 THEN 'Williams' WHEN 3 THEN 'Brown' WHEN 4 THEN 'Jones'
            WHEN 5 THEN 'Garcia' WHEN 6 THEN 'Miller' WHEN 7 THEN 'Davis' WHEN 8 THEN 'Rodriguez' WHEN 9 THEN 'Martinez'
            WHEN 10 THEN 'Hernandez' WHEN 11 THEN 'Lopez' WHEN 12 THEN 'Gonzalez' WHEN 13 THEN 'Wilson' WHEN 14 THEN 'Anderson'
            WHEN 15 THEN 'Thomas' WHEN 16 THEN 'Taylor' WHEN 17 THEN 'Moore' WHEN 18 THEN 'Jackson' WHEN 19 THEN 'Martin'
            WHEN 20 THEN 'Lee' WHEN 21 THEN 'Perez' WHEN 22 THEN 'Thompson' WHEN 23 THEN 'White' WHEN 24 THEN 'Harris'
            WHEN 25 THEN 'Clark' WHEN 26 THEN 'Lewis' WHEN 27 THEN 'Robinson' WHEN 28 THEN 'Walker' WHEN 29 THEN 'Young'
        END,
        Email = 'contact' + CAST(ABS(CHECKSUM(NEWID())) % 100000 AS VARCHAR(10)) + '@anonymized.local',
        PhoneNo = '******-' + RIGHT('000' + CAST(ABS(CHECKSUM(NEWID())) % 10000 AS VARCHAR(4)), 4),
        Address = 
            CAST(ABS(CHECKSUM(NEWID())) % 9999 + 1 AS VARCHAR(4)) + ' ' +
            CASE ABS(CHECKSUM(NEWID())) % 10
                WHEN 0 THEN 'Main St'
                WHEN 1 THEN 'Oak Ave'
                WHEN 2 THEN 'Park Rd'
                WHEN 3 THEN 'First St'
                WHEN 4 THEN 'Second Ave'
                WHEN 5 THEN 'Third Blvd'
                WHEN 6 THEN 'Fourth Way'
                WHEN 7 THEN 'Fifth Pl'
                WHEN 8 THEN 'Sixth Dr'
                ELSE 'Seventh Ln'
            END + ', Anytown, ST 12345';
    
    SET @RowCount = @@ROWCOUNT;
    PRINT '    Updated ' + CAST(@RowCount AS VARCHAR(10)) + ' records in ContactPersonInformation table.';
    
    -- Anonymize GOSecurity.GOUser table
    PRINT '  - Anonymizing GOSecurity.GOUser table...';
    UPDATE [GOSecurity].[GOUser] SET
        UserName = 'user' + CAST(ABS(CHECKSUM(NEWID())) % 100000 AS VARCHAR(10)),
        EmailAddress = 'gouser' + CAST(ABS(CHECKSUM(NEWID())) % 100000 AS VARCHAR(10)) + '@anonymized.local',
        NewEmailAddress = CASE 
            WHEN NewEmailAddress IS NOT NULL THEN 
                'newuser' + CAST(ABS(CHECKSUM(NEWID())) % 100000 AS VARCHAR(10)) + '@anonymized.local'
            ELSE NULL 
        END,
        FullName = CASE ABS(CHECKSUM(NEWID())) % 50
            WHEN 0 THEN 'Alex' WHEN 1 THEN 'Blake' WHEN 2 THEN 'Casey' WHEN 3 THEN 'Drew' WHEN 4 THEN 'Ellis'
            WHEN 5 THEN 'Finley' WHEN 6 THEN 'Harley' WHEN 7 THEN 'Indigo' WHEN 8 THEN 'Jules' WHEN 9 THEN 'Kai'
            WHEN 10 THEN 'Lane' WHEN 11 THEN 'Morgan' WHEN 12 THEN 'Nova' WHEN 13 THEN 'Ocean' WHEN 14 THEN 'Phoenix'
            WHEN 15 THEN 'Quinn' WHEN 16 THEN 'River' WHEN 17 THEN 'Sage' WHEN 18 THEN 'Tatum' WHEN 19 THEN 'Unity'
            WHEN 20 THEN 'Vale' WHEN 21 THEN 'Winter' WHEN 22 THEN 'Xen' WHEN 23 THEN 'York' WHEN 24 THEN 'Zen'
            WHEN 25 THEN 'Ash' WHEN 26 THEN 'Bay' WHEN 27 THEN 'Cloud' WHEN 28 THEN 'Dale' WHEN 29 THEN 'Echo'
            WHEN 30 THEN 'Fox' WHEN 31 THEN 'Grey' WHEN 32 THEN 'Haze' WHEN 33 THEN 'Ivy' WHEN 34 THEN 'Jazz'
            WHEN 35 THEN 'Knox' WHEN 36 THEN 'Moon' WHEN 37 THEN 'North' WHEN 38 THEN 'Onyx' WHEN 39 THEN 'Pine'
            WHEN 40 THEN 'Quest' WHEN 41 THEN 'Rain' WHEN 42 THEN 'Stone' WHEN 43 THEN 'Truth' WHEN 44 THEN 'Urban'
            WHEN 45 THEN 'Violet' WHEN 46 THEN 'Wave' WHEN 47 THEN 'Xylem' WHEN 48 THEN 'Yarrow' ELSE 'Zion'
        END + ' ' + CASE ABS(CHECKSUM(NEWID())) % 30
            WHEN 0 THEN 'Johnson' WHEN 1 THEN 'Smith' WHEN 2 THEN 'Williams' WHEN 3 THEN 'Brown' WHEN 4 THEN 'Jones'
            WHEN 5 THEN 'Garcia' WHEN 6 THEN 'Miller' WHEN 7 THEN 'Davis' WHEN 8 THEN 'Rodriguez' WHEN 9 THEN 'Martinez'
            WHEN 10 THEN 'Hernandez' WHEN 11 THEN 'Lopez' WHEN 12 THEN 'Gonzalez' WHEN 13 THEN 'Wilson' WHEN 14 THEN 'Anderson'
            WHEN 15 THEN 'Thomas' WHEN 16 THEN 'Taylor' WHEN 17 THEN 'Moore' WHEN 18 THEN 'Jackson' WHEN 19 THEN 'Martin'
            WHEN 20 THEN 'Lee' WHEN 21 THEN 'Perez' WHEN 22 THEN 'Thompson' WHEN 23 THEN 'White' WHEN 24 THEN 'Harris'
            WHEN 25 THEN 'Clark' WHEN 26 THEN 'Lewis' WHEN 27 THEN 'Robinson' WHEN 28 THEN 'Walker' WHEN 29 THEN 'Young'
        END,
        FirstName = CASE ABS(CHECKSUM(NEWID())) % 50
            WHEN 0 THEN 'Alex' WHEN 1 THEN 'Blake' WHEN 2 THEN 'Casey' WHEN 3 THEN 'Drew' WHEN 4 THEN 'Ellis'
            WHEN 5 THEN 'Finley' WHEN 6 THEN 'Harley' WHEN 7 THEN 'Indigo' WHEN 8 THEN 'Jules' WHEN 9 THEN 'Kai'
            WHEN 10 THEN 'Lane' WHEN 11 THEN 'Morgan' WHEN 12 THEN 'Nova' WHEN 13 THEN 'Ocean' WHEN 14 THEN 'Phoenix'
            WHEN 15 THEN 'Quinn' WHEN 16 THEN 'River' WHEN 17 THEN 'Sage' WHEN 18 THEN 'Tatum' WHEN 19 THEN 'Unity'
            WHEN 20 THEN 'Vale' WHEN 21 THEN 'Winter' WHEN 22 THEN 'Xen' WHEN 23 THEN 'York' WHEN 24 THEN 'Zen'
            WHEN 25 THEN 'Ash' WHEN 26 THEN 'Bay' WHEN 27 THEN 'Cloud' WHEN 28 THEN 'Dale' WHEN 29 THEN 'Echo'
            WHEN 30 THEN 'Fox' WHEN 31 THEN 'Grey' WHEN 32 THEN 'Haze' WHEN 33 THEN 'Ivy' WHEN 34 THEN 'Jazz'
            WHEN 35 THEN 'Knox' WHEN 36 THEN 'Moon' WHEN 37 THEN 'North' WHEN 38 THEN 'Onyx' WHEN 39 THEN 'Pine'
            WHEN 40 THEN 'Quest' WHEN 41 THEN 'Rain' WHEN 42 THEN 'Stone' WHEN 43 THEN 'Truth' WHEN 44 THEN 'Urban'
            WHEN 45 THEN 'Violet' WHEN 46 THEN 'Wave' WHEN 47 THEN 'Xylem' WHEN 48 THEN 'Yarrow' ELSE 'Zion'
        END,
        LastName = CASE ABS(CHECKSUM(NEWID())) % 30
            WHEN 0 THEN 'Johnson' WHEN 1 THEN 'Smith' WHEN 2 THEN 'Williams' WHEN 3 THEN 'Brown' WHEN 4 THEN 'Jones'
            WHEN 5 THEN 'Garcia' WHEN 6 THEN 'Miller' WHEN 7 THEN 'Davis' WHEN 8 THEN 'Rodriguez' WHEN 9 THEN 'Martinez'
            WHEN 10 THEN 'Hernandez' WHEN 11 THEN 'Lopez' WHEN 12 THEN 'Gonzalez' WHEN 13 THEN 'Wilson' WHEN 14 THEN 'Anderson'
            WHEN 15 THEN 'Thomas' WHEN 16 THEN 'Taylor' WHEN 17 THEN 'Moore' WHEN 18 THEN 'Jackson' WHEN 19 THEN 'Martin'
            WHEN 20 THEN 'Lee' WHEN 21 THEN 'Perez' WHEN 22 THEN 'Thompson' WHEN 23 THEN 'White' WHEN 24 THEN 'Harris'
            WHEN 25 THEN 'Clark' WHEN 26 THEN 'Lewis' WHEN 27 THEN 'Robinson' WHEN 28 THEN 'Walker' WHEN 29 THEN 'Young'
        END,
        Password = 'ANONYMIZED_PASSWORD_HASH_' + CAST(ABS(CHECKSUM(NEWID())) % 1000000 AS VARCHAR(10)),
        ExternalUserId = CASE 
            WHEN ExternalUserId IS NOT NULL THEN 
                'ext_' + CAST(ABS(CHECKSUM(NEWID())) % 1000000 AS VARCHAR(10))
            ELSE NULL 
        END;
    
    SET @RowCount = @@ROWCOUNT;
    PRINT '    Updated ' + CAST(@RowCount AS VARCHAR(10)) + ' records in GOSecurity.GOUser table.';
    
    -- Anonymize WebsiteUser table
    PRINT '  - Anonymizing WebsiteUser table...';
    UPDATE WebsiteUser SET
        Username = 'webuser' + CAST(ABS(CHECKSUM(NEWID())) % 100000 AS VARCHAR(10)),
        Password = 'ANONYMIZED_WEB_PASSWORD_' + CAST(ABS(CHECKSUM(NEWID())) % 1000000 AS VARCHAR(10));
    
    SET @RowCount = @@ROWCOUNT;
    PRINT '    Updated ' + CAST(@RowCount AS VARCHAR(10)) + ' records in WebsiteUser table.';
    
    PRINT 'Step 2 completed successfully.';
    PRINT '';
    
    -- =====================================================================================================================
    -- STEP 3: ANONYMIZE SECONDARY PERSONAL INFORMATION
    -- =====================================================================================================================
    
    PRINT 'Step 3: Anonymizing secondary personal information...';
    
    -- Anonymize Customer table
    PRINT '  - Anonymizing Customer table...';
    UPDATE Customer SET
        ContactNumber = CASE 
            WHEN ContactNumber IS NOT NULL THEN 
                '******-' + RIGHT('000' + CAST(ABS(CHECKSUM(NEWID())) % 10000 AS VARCHAR(4)), 4)
            ELSE NULL 
        END,
        Email = CASE 
            WHEN Email IS NOT NULL THEN 
                'customer' + CAST(ABS(CHECKSUM(NEWID())) % 100000 AS VARCHAR(10)) + '@anonymized.local'
            ELSE NULL 
        END,
        Addess = CASE 
            WHEN Addess IS NOT NULL THEN 
                CAST(ABS(CHECKSUM(NEWID())) % 9999 + 1 AS VARCHAR(4)) + ' Business Park Dr, Corporate City, ST 12345'
            ELSE NULL 
        END,
        CompanyName = CASE ABS(CHECKSUM(NEWID())) % 30
            WHEN 0 THEN 'Alpha Industries' WHEN 1 THEN 'Beta Corp' WHEN 2 THEN 'Gamma LLC' WHEN 3 THEN 'Delta Systems' WHEN 4 THEN 'Epsilon Ltd'
            WHEN 5 THEN 'Zeta Technologies' WHEN 6 THEN 'Eta Solutions' WHEN 7 THEN 'Theta Enterprises' WHEN 8 THEN 'Iota Group' WHEN 9 THEN 'Kappa Inc'
            WHEN 10 THEN 'Lambda Holdings' WHEN 11 THEN 'Mu Partners' WHEN 12 THEN 'Nu Ventures' WHEN 13 THEN 'Xi Corporation' WHEN 14 THEN 'Omicron Co'
            WHEN 15 THEN 'Pi Dynamics' WHEN 16 THEN 'Rho Industries' WHEN 17 THEN 'Sigma Works' WHEN 18 THEN 'Tau Systems' WHEN 19 THEN 'Upsilon Ltd'
            WHEN 20 THEN 'Phi Technologies' WHEN 21 THEN 'Chi Solutions' WHEN 22 THEN 'Psi Group' WHEN 23 THEN 'Omega Corp' WHEN 24 THEN 'Apex Industries'
            WHEN 25 THEN 'Vertex Systems' WHEN 26 THEN 'Matrix Corp' WHEN 27 THEN 'Nexus Ltd' WHEN 28 THEN 'Vortex Inc' ELSE 'Zenith Holdings'
        END;
    
    SET @RowCount = @@ROWCOUNT;
    PRINT '    Updated ' + CAST(@RowCount AS VARCHAR(10)) + ' records in Customer table.';
    
    -- Anonymize Card table
    PRINT '  - Anonymizing Card table...';
    UPDATE Card SET
        CardNumber = 'CARD' + RIGHT('0000000' + CAST(ABS(CHECKSUM(NEWID())) % 10000000 AS VARCHAR(7)), 7),
        Weigand = CASE 
            WHEN Weigand IS NOT NULL THEN 
                CAST(ABS(CHECKSUM(NEWID())) % 100000 AS VARCHAR(10))
            ELSE NULL 
        END,
        FacilityCode = CASE 
            WHEN FacilityCode IS NOT NULL THEN 
                RIGHT('00' + CAST(ABS(CHECKSUM(NEWID())) % 1000 AS VARCHAR(3)), 3)
            ELSE NULL 
        END;
    
    SET @RowCount = @@ROWCOUNT;
    PRINT '    Updated ' + CAST(@RowCount AS VARCHAR(10)) + ' records in Card table.';
    
    -- Anonymize LicenceDetail table
    PRINT '  - Anonymizing LicenceDetail table...';
    UPDATE LicenceDetail SET
        LicenseNumber = CASE 
            WHEN LicenseNumber IS NOT NULL THEN 
                'DL' + RIGHT('00000000' + CAST(ABS(CHECKSUM(NEWID())) % 100000000 AS VARCHAR(8)), 8)
            ELSE NULL 
        END,
        Document = NULL,
        DocumentFileSize = NULL,
        DocumentInternalName = NULL;
    
    SET @RowCount = @@ROWCOUNT;
    PRINT '    Updated ' + CAST(@RowCount AS VARCHAR(10)) + ' records in LicenceDetail table.';
    
    -- Anonymize Module table
    PRINT '  - Anonymizing Module table...';
    UPDATE Module SET
        SimCardNumber = CASE 
            WHEN SimCardNumber IS NOT NULL THEN 
                'SIM' + RIGHT('000000000000000' + CAST(ABS(CHECKSUM(NEWID())) % CAST('1000000000000000' AS BIGINT) AS VARCHAR(15)), 15)
            ELSE NULL 
        END,
        CCID = CASE 
            WHEN CCID IS NOT NULL THEN 
                'CCID' + RIGHT('00000000000000000000' + CAST(ABS(CHECKSUM(NEWID())) % CAST('100000000000000000000' AS BIGINT) AS VARCHAR(20)), 20)
            ELSE NULL 
        END,
        TechNumber = CASE 
            WHEN TechNumber IS NOT NULL THEN 
                'TECH' + RIGHT('000000' + CAST(ABS(CHECKSUM(NEWID())) % 1000000 AS VARCHAR(6)), 6)
            ELSE NULL 
        END,
        Note = CASE 
            WHEN Note IS NOT NULL THEN 'Anonymized technical note'
            ELSE NULL 
        END;
    
    SET @RowCount = @@ROWCOUNT;
    PRINT '    Updated ' + CAST(@RowCount AS VARCHAR(10)) + ' records in Module table.';
    
    -- Anonymize GOSecurity.GOUser2FA table
    PRINT '  - Anonymizing GOSecurity.GOUser2FA table...';
    UPDATE [GOSecurity].[GOUser2FA] SET
        OTPSecret = CASE 
            WHEN OTPSecret IS NOT NULL THEN 
                'ANON_OTP_' + CAST(ABS(CHECKSUM(NEWID())) % 1000000 AS VARCHAR(10))
            ELSE NULL 
        END;
    
    SET @RowCount = @@ROWCOUNT;
    PRINT '    Updated ' + CAST(@RowCount AS VARCHAR(10)) + ' records in GOSecurity.GOUser2FA table.';
    
    -- Anonymize GOSecurity.GOLoginHistory table
    PRINT '  - Anonymizing GOSecurity.GOLoginHistory table...';
    UPDATE [GOSecurity].[GOLoginHistory] SET
        [User] = 'anonuser' + CAST(ABS(CHECKSUM(NEWID())) % 100000 AS VARCHAR(10)),
        Info = 'Anonymized login info';
    
    SET @RowCount = @@ROWCOUNT;
    PRINT '    Updated ' + CAST(@RowCount AS VARCHAR(10)) + ' records in GOSecurity.GOLoginHistory table.';
    
    PRINT 'Step 3 completed successfully.';
    PRINT '';
    
    -- =====================================================================================================================
    -- STEP 4: ANONYMIZE LOCATION AND ACTIVITY DATA
    -- =====================================================================================================================
    
    PRINT 'Step 4: Anonymizing location and activity data...';
    
    -- Anonymize GPSHistory table - offset coordinates by random amounts
    PRINT '  - Anonymizing GPSHistory table...';
    UPDATE GPSHistory SET
        Latitude = CASE 
            WHEN Latitude IS NOT NULL THEN 
                40 + (ABS(CHECKSUM(NEWID())) % 100) / 100.0  -- Random latitude around 40° (generic US location)
            ELSE NULL 
        END,
        Longitude = CASE 
            WHEN Longitude IS NOT NULL THEN 
                -100 + (ABS(CHECKSUM(NEWID())) % 100) / 100.0  -- Random longitude around -100° (generic US location)
            ELSE NULL 
        END;
    
    SET @RowCount = @@ROWCOUNT;
    PRINT '    Updated ' + CAST(@RowCount AS VARCHAR(10)) + ' records in GPSHistory table.';
    
    -- Anonymize VehicleGPS table - offset coordinates by random amounts
    PRINT '  - Anonymizing VehicleGPS table...';
    UPDATE VehicleGPS SET
        Latitude = 40 + (ABS(CHECKSUM(NEWID())) % 100) / 100.0,  -- Random latitude around 40°
        Longitude = -100 + (ABS(CHECKSUM(NEWID())) % 100) / 100.0;  -- Random longitude around -100°
    
    SET @RowCount = @@ROWCOUNT;
    PRINT '    Updated ' + CAST(@RowCount AS VARCHAR(10)) + ' records in VehicleGPS table.';
    
    -- Note: Session and Impact tables contain behavioral data but not direct personal identifiers
    -- The foreign key relationships will maintain referential integrity while the linked personal data is anonymized
    
    PRINT 'Step 4 completed successfully.';
    PRINT '';
    
    -- =====================================================================================================================
    -- STEP 5: ANONYMIZE AUDIT AND TRACKING INFORMATION
    -- =====================================================================================================================
    
    PRINT 'Step 5: Anonymizing audit and tracking information...';
    
    -- Anonymize GOChangeTracking.CustomerAudit table
    -- Note: We preserve the GUID structure but replace with anonymized GUIDs to maintain referential integrity
    PRINT '  - Anonymizing GOChangeTracking.CustomerAudit table...';
    IF EXISTS (SELECT 1 FROM sys.tables t JOIN sys.schemas s ON t.schema_id = s.schema_id 
               WHERE s.name = 'GOChangeTracking' AND t.name = 'CustomerAudit')
    BEGIN
        -- Update CustomerAudit with anonymized GUIDs (direct anonymization without temporary tables)
        UPDATE [GOChangeTracking].[CustomerAudit] SET
            CreatedBy = CASE WHEN CreatedBy IS NOT NULL THEN NEWID() ELSE NULL END,
            LastModifiedBy = CASE WHEN LastModifiedBy IS NOT NULL THEN NEWID() ELSE NULL END,
            DeletedBy = CASE WHEN DeletedBy IS NOT NULL THEN NEWID() ELSE NULL END;
        
        SET @RowCount = @@ROWCOUNT;
        PRINT '    Updated ' + CAST(@RowCount AS VARCHAR(10)) + ' records in GOChangeTracking.CustomerAudit table.';
    END
    ELSE
    BEGIN
        PRINT '    GOChangeTracking.CustomerAudit table not found - skipping.';
    END
    
    -- Anonymize GOChangeTracking.GOChangeDelta table
    PRINT '  - Anonymizing GOChangeTracking.GOChangeDelta table...';
    IF EXISTS (SELECT 1 FROM sys.tables t JOIN sys.schemas s ON t.schema_id = s.schema_id 
               WHERE s.name = 'GOChangeTracking' AND t.name = 'GOChangeDelta')
    BEGIN
        UPDATE [GOChangeTracking].[GOChangeDelta] SET
            Who = 'anonymized_user_' + CAST(ABS(CHECKSUM(NEWID())) % 100000 AS VARCHAR(10));
        
        SET @RowCount = @@ROWCOUNT;
        PRINT '    Updated ' + CAST(@RowCount AS VARCHAR(10)) + ' records in GOChangeTracking.GOChangeDelta table.';
    END
    ELSE
    BEGIN
        PRINT '    GOChangeTracking.GOChangeDelta table not found - skipping.';
    END
    
    PRINT 'Step 5 completed successfully.';
    PRINT '';
    
    -- =====================================================================================================================
    -- STEP 6: CLEAN UP TEMPORARY OBJECTS
    -- =====================================================================================================================
    
    PRINT 'Step 6: Finalizing anonymization process...';
    
    -- No temporary objects to clean up - all anonymization done via direct UPDATE statements
    
    PRINT 'Anonymization process finalized successfully.';
    PRINT '';
    
    -- =====================================================================================================================
    -- COMMIT TRANSACTION AND COMPLETION
    -- =====================================================================================================================
    
    COMMIT TRANSACTION AnonymizationTransaction;
    
    DECLARE @EndTime DATETIME = GETDATE();
    DECLARE @Duration VARCHAR(20) = CAST(DATEDIFF(SECOND, @StartTime, @EndTime) AS VARCHAR(10));
    
    PRINT '=== ANONYMIZATION COMPLETED SUCCESSFULLY ===';
    PRINT 'Start Time: ' + CONVERT(VARCHAR, @StartTime, 120);
    PRINT 'End Time: ' + CONVERT(VARCHAR, @EndTime, 120);
    PRINT 'Duration: ' + @Duration + ' seconds';
    PRINT '';
    PRINT 'All personal data has been anonymized according to the specified requirements.';
    PRINT 'Referential integrity has been maintained throughout the process.';
    
END TRY
BEGIN CATCH
    -- =====================================================================================================================
    -- ERROR HANDLING AND ROLLBACK
    -- =====================================================================================================================
    
    IF @@TRANCOUNT > 0
    BEGIN
        ROLLBACK TRANSACTION AnonymizationTransaction;
        PRINT 'TRANSACTION ROLLED BACK due to error.';
    END
    
    SET @ErrorMessage = ERROR_MESSAGE();
    DECLARE @ErrorNumber INT = ERROR_NUMBER();
    DECLARE @ErrorSeverity INT = ERROR_SEVERITY();
    DECLARE @ErrorState INT = ERROR_STATE();
    DECLARE @ErrorLine INT = ERROR_LINE();
    
    PRINT '';
    PRINT '=== ANONYMIZATION FAILED ===';
    PRINT 'Error Number: ' + CAST(@ErrorNumber AS VARCHAR(10));
    PRINT 'Error Severity: ' + CAST(@ErrorSeverity AS VARCHAR(10));
    PRINT 'Error State: ' + CAST(@ErrorState AS VARCHAR(10));
    PRINT 'Error Line: ' + CAST(@ErrorLine AS VARCHAR(10));
    PRINT 'Error Message: ' + @ErrorMessage;
    PRINT '';
    PRINT 'All changes have been rolled back. No data was modified.';
    PRINT 'Please review the error and retry the anonymization process.';
    
    -- No temporary objects to clean up - all processing done via direct UPDATE statements
    
    RAISERROR(@ErrorMessage, @ErrorSeverity, @ErrorState);
END CATCH

SET NOCOUNT OFF;

-- =====================================================================================================================
-- POST-ANONYMIZATION VERIFICATION QUERIES
-- =====================================================================================================================
-- Uncomment and run these queries after anonymization to verify the results:

/*
-- Verify Person table anonymization
SELECT TOP 5 FirstName, LastName, Email, Phone FROM Person;

-- Verify Customer table anonymization  
SELECT TOP 5 CompanyName, Email, ContactNumber FROM Customer;

-- Verify GOUser table anonymization
SELECT TOP 5 UserName, EmailAddress, FullName FROM [GOSecurity].[GOUser];

-- Check for any remaining potentially sensitive data patterns
SELECT 'Person' as TableName, COUNT(*) as Records,
       SUM(CASE WHEN Email LIKE '%@anonymized.local' THEN 1 ELSE 0 END) as AnonymizedEmails
FROM Person
WHERE Email IS NOT NULL
UNION ALL
SELECT 'Customer', COUNT(*), 
       SUM(CASE WHEN Email LIKE '%@anonymized.local' THEN 1 ELSE 0 END)
FROM Customer  
WHERE Email IS NOT NULL
UNION ALL
SELECT 'GOUser', COUNT(*),
       SUM(CASE WHEN EmailAddress LIKE '%@anonymized.local' THEN 1 ELSE 0 END)
FROM [GOSecurity].[GOUser]
WHERE EmailAddress IS NOT NULL;
*/
